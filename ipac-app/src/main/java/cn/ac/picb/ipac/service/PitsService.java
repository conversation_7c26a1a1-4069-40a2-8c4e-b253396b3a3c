package cn.ac.picb.ipac.service;

import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.enums.PitsTaskStatusEnum;
import cn.ac.picb.ipac.common.enums.TaskType;
import cn.ac.picb.ipac.common.util.DateUtils;
import cn.ac.picb.ipac.common.util.DownloadUtils;
import cn.ac.picb.ipac.common.util.IdUtil;
import cn.ac.picb.ipac.config.FileProperties;
import cn.ac.picb.ipac.dto.PitsTaskDTO;
import cn.ac.picb.ipac.dto.VenasTaskSearch;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.mq.msg.*;
import cn.ac.picb.ipac.mq.pits.PitsMessageSender;
import cn.ac.picb.ipac.repository.UserTaskRepository;
import cn.ac.picb.ipac.vo.UserTaskVo;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2025/8/21
 */
@Service
@Slf4j
public class PitsService extends TaskService {
    @Autowired
    private FileProperties fileProperties;

    @Autowired
    private UserTaskRepository userTaskRepository;

    @Autowired
    private PitsMessageSender messageSender;


    @Transactional(rollbackFor = Exception.class)
    public UserTask addTask(User user, PitsTaskDTO dto) {
        UserTask userTask = saveTaskToDb(user, dto);

        messageSender.sendTaskCreateMsg(new TaskCreateMsg(user.getAccountName(), userTask.getTaskId()));

        return userTask;
    }

    private UserTask saveTaskToDb(User user, PitsTaskDTO dto) {
        UserTask userTask = new UserTask();
        userTask.setId(IdUtil.getShortUUID());
        String taskId = "PI" + getTaskId();
        userTask.setTaskId(taskId);
        userTask.setUser(user);
        Date date = new Date();
        userTask.setType(TaskType.pits.name());
        userTask.setCreateTime(date);
        userTask.setModifyTime(date);
        userTask.setStatus(PitsTaskStatusEnum.draft.getCode());
        userTask.setInputPath(JSONObject.toJSONString(dto));
        userTaskRepository.saveAndFlush(userTask);

        addTaskStatusFlow(PitsTaskStatusEnum.draft.getCode(), userTask);
        return userTask;
    }

    public void handlerStatusChange(StatusMsg msg) {
        String taskId = msg.getTaskId();
        Integer statusCode = msg.getStatusCode();

        if (statusCode == 20000) {
            log.error("分析服务分析出错：{}", msg.getMessage());
        } else if (statusCode == 30000) {
            log.error("task 移动文件出错：{}", msg.getMessage());
        }
        PitsTaskStatusEnum statusEnum = PitsTaskStatusEnum.getEnum(statusCode);
        if (statusEnum == null) {
            statusEnum = PitsTaskStatusEnum.error;
        }

        UserTask task = findByTaskId(taskId);

        // 已删除，不处理
        if (PitsTaskStatusEnum.delete.getCode().equals(task.getStatus())) {
            return;
        }
        // 防止重复消费消息
        if (!PitsTaskStatusEnum.error.equals(statusEnum) && task.getStatus() > statusEnum.getCode()) {
            return;
        }
        task.setStatus(statusEnum.getCode());
        task.setModifyTime(new Date());

        if (PitsTaskStatusEnum.ready.equals(statusEnum)) {
            // 发送分析任务开始消息
            // 发送开始的消息
            messageSender.sendAnalysisStartMsg(new AnalysisStartMsg(taskId));
        } else if (PitsTaskStatusEnum.complete.equals(statusEnum)) {
            task.setOutPath("/" + task.getTaskId());
            messageSender.sendTaskFinishedMsg(new TaskFinishedMsg(task.getId(), task.getTaskId()));
        } else if (PitsTaskStatusEnum.error.equals(statusEnum)) {
            task.setErrMsg(msg.getMessage());
        }
        userTaskRepository.save(task);

        addTaskStatusFlow(statusCode, task);
    }

    public void handlerTaskCreate(TaskCreateMsg msg) {
        String taskId = msg.getTaskId();
        UserTask task = findByTaskId(taskId);
        PitsTaskDTO dto = JSONUtil.toBean(task.getInputPath(), PitsTaskDTO.class);

        // 输入输出文件夹
        File inputDir = FileUtil.file(fileProperties.getInputHome(), fileProperties.getPitsDirName(), taskId);

        // 把输入的文件拷贝的文件复制进去
        File inputFile = FileUtil.file(inputDir, dto.getInputFile().getSampleName() + ".fasta");
        String username = msg.getUsername();
        String ftpHome = fileProperties.getFtpHome() + File.separator + username;

        FileUtil.copy(
                FileUtil.file(ftpHome, dto.getInputFile().getFile().getPath()),
                inputFile,
                true
        );

        // 写入到params.json
        cn.hutool.json.JSONObject entries = new cn.hutool.json.JSONObject();
        entries.put("input", StrUtil.format("{}/{}/{}", fileProperties.getPitsServerInputDataHome(), taskId, inputFile.getName()));
        entries.put("outdir", StrUtil.format("{}/{}", fileProperties.getPitsServerOutputDataHome(), taskId));
        FileUtil.writeUtf8String(entries.toStringPretty(), FileUtil.file(inputDir, "params.json"));
    }

    public void handlerAnalysisResult(AnalysisResultMsg msg) {
        log.info("===handlerAnalysisResult skip===");
    }

    public Page<UserTaskVo> findPitsTaskPage(User user, Pageable pageable, VenasTaskSearch search) {
        Page<UserTask> page = userTaskRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (StringUtils.isNoneBlank(search.getTaskId())) {
                predicateList.add(cb.equal(root.get("taskId"), search.getTaskId()));
            }
            predicateList.add(cb.equal(root.get("type"), TaskType.pits.name()));
            if (StringUtils.isNoneBlank(search.getStatusDesc())) {
                predicateList.add(cb.equal(root.get("status"), search.getStatusDesc()));
            }
            if (search.getCreateTimeStart() != null) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeStart(), "yyyy-MM-dd") + " 00:00:00")));
            }
            if (search.getCreateTimeEnd() != null) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeEnd(), "yyyy-MM-dd") + " 23:59:59")));
            }
            predicateList.add(cb.equal(root.get("user").get("id"), user.getId()));
            predicateList.add(cb.notEqual(root.get("status"), PitsTaskStatusEnum.delete.getCode()));
            query.where(predicateList.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createTime")));
            return null;
        }, pageable);
        List<UserTask> content = page.getContent();
        List<UserTaskVo> voList = new ArrayList<>();
        for (UserTask userTask : content) {
            UserTaskVo vo = new UserTaskVo();
            voList.add(vo);
            vo.setUserTask(userTask);
            vo.setUseTime(getUseTime(userTask.getCreateTime(), userTask.getModifyTime()));
        }
        return new PageImpl<>(voList, pageable, page.getTotalElements());
    }

    public void downloadResult(String id, String userId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserTask task = findUserTaskById(id);
        if (!task.getUser().getId().equals(userId)) {
            throw new ServiceException(StrUtil.format("任务创建人ID和当前用户ID不一致：{} {}", task.getUser().getId(), userId));
        }
        String taskId = task.getTaskId();
        File file = FileUtil.file(fileProperties.getOutputHome(), fileProperties.getPitsDirName(), taskId, "report.pdf");
        DownloadUtils.download(request, response, file);
    }
}
