package cn.ac.picb.ipac.controller;

import cn.ac.picb.ipac.common.core.Result;
import cn.ac.picb.ipac.common.core.ResultGenerator;
import cn.ac.picb.ipac.common.enums.GvapTaskStatusEnum;
import cn.ac.picb.ipac.dto.GvapTaskDTO;
import cn.ac.picb.ipac.dto.VenasTaskSearch;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.service.GvapService;
import cn.ac.picb.ipac.vo.UserTaskVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR> Li
 * @date 2025/8/19
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@RequestMapping("/gvap")
public class GvapController extends BaseController {


    private final GvapService gvapService;


    @RequestMapping("/main")
    public String main(Model model) {
        return "gvap/main";
    }

    @RequestMapping("/addTask")
    @ResponseBody
    public Result gvapAddTask(@Validated GvapTaskDTO dto) {
        UserTask task = gvapService.addTask(getUser(), dto);
        return ResultGenerator.genSuccessResult();
    }

    /**
     * GVAP任务列表
     *
     * @param model
     * @param pageable
     * @return
     */
    @RequestMapping("/tasks")
    public String gvapTasks(Model model, @PageableDefault Pageable pageable, @ModelAttribute("search") VenasTaskSearch search) {
        Page<UserTaskVo> page = gvapService.findGvapTaskPage(getUser(), pageable, search);
        Map<Integer, String> code$DescMap = GvapTaskStatusEnum.statusMap("en");
        model.addAttribute("codeDescMap", code$DescMap);
        model.addAttribute("page", page);

        return "user/gvap/task-list";
    }

    /**
     * GVAP任务列表-下载结果
     *
     * @param id
     * @return
     */
    @RequestMapping("/downLoadResult")
    public void gvapDownLoadResult(String id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        gvapService.downloadResult(id, getUser().getId(), request, response);
    }

    /**
     * 下载excel 模板
     */
    @GetMapping("/downloadTemplateExcel")
    public void downloadTemplateExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        gvapService.downloadTemplateExcel(request, response);
    }

    /**
     * 上传excel模板
     */
    @PostMapping("/uploadDataExcel")
    @ResponseBody
    public Result uploadDataExcel(MultipartFile excel) {
        // 验证文件参数
        if (excel == null || excel.isEmpty()) {
            return ResultGenerator.genFailResult("Excel file is required and cannot be empty");
        }

        // 验证用户认证
        User currentUser = getUser();
        if (currentUser == null) {
            return ResultGenerator.genFailResult("User not authenticated. Please login first.");
        }

        // 验证用户账户名
        String accountName = currentUser.getAccountName();
        if (accountName == null || accountName.trim().isEmpty()) {
            return ResultGenerator.genFailResult("User account name is not available");
        }

        try {
            GvapTaskDTO result = gvapService.uploadDataExcel(excel, accountName);
            return ResultGenerator.genSuccessResult(result);
        } catch (Exception e) {
            // 记录详细错误信息用于调试
            log.error("Error uploading Excel file for user {}: {}", accountName, e.getMessage(), e);
            return ResultGenerator.genFailResult("Failed to process Excel file: " + e.getMessage());
        }
    }
}
